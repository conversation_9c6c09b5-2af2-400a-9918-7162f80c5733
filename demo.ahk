#Requires AutoHotkey v2.0

; 游戏宏控制面板演示程序
; 展示完整的GUI界面和功能

; 引入依赖模块
#Include includes\ProcessUtils.ahk
#Include includes\GuiUtils.ahk
#Include includes\ConfigManager.ahk
#Include includes\Logger.ahk

; 全局变量
global DemoGui := ""
global AppLogger := ""
global Config := ""

; 启动演示
StartDemo()

; 演示主函数
StartDemo() {
    ; 初始化日志系统
    AppLogger := Logger("logs\demo.log", Logger.INFO)
    AppLogger.Info("演示程序启动")

    ; 初始化配置管理器
    Config := ConfigManager("demo_config.ini")

    ; 创建演示GUI
    CreateDemoGUI()

    ; 显示界面
    DemoGui.Show("w500 h400", "游戏宏控制面板 - 功能演示")

    AppLogger.Info("演示界面已显示")
}

; 创建演示GUI
CreateDemoGUI() {
    ; 创建主窗口
    DemoGui := Gui("+Resize", "功能演示")
    DemoGui.OnEvent("Close", (*) => ExitApp())
    
    ; 标题区域
    CreateTitleText(DemoGui, 10, 10, 480, "游戏宏控制面板功能演示")
    CreateSeparator(DemoGui, 10, 45, 480)
    
    ; 进程检测演示区域
    DemoGui.Add("Text", "x10 y55 w200 h20 Section", "进程检测功能演示:")
    local ProcessTestBtn := DemoGui.Add("Button", "x220 y55 w100 h25", "测试进程检测")
    ProcessTestBtn.OnEvent("Click", TestProcessDetection)
    
    local ProcessResult := DemoGui.Add("Text", "x330 y55 w150 h25 cBlue", "[等待测试]")
    
    ; GUI工具演示区域
    DemoGui.Add("Text", "x10 y85 w200 h20", "GUI工具功能演示:")
    local GuiTestBtn := DemoGui.Add("Button", "x220 y85 w100 h25", "测试消息框")
    GuiTestBtn.OnEvent("Click", TestGuiUtils)
    
    ; 配置管理演示区域
    DemoGui.Add("Text", "x10 y115 w200 h20", "配置管理功能演示:")
    local ConfigTestBtn := DemoGui.Add("Button", "x220 y115 w100 h25", "测试配置读写")
    ConfigTestBtn.OnEvent("Click", TestConfigManager)
    
    local ConfigResult := DemoGui.Add("Text", "x330 y115 w150 h25 cGreen", "[配置已加载]")
    
    ; 日志系统演示区域
    DemoGui.Add("Text", "x10 y145 w200 h20", "日志系统功能演示:")
    local LogTestBtn := DemoGui.Add("Button", "x220 y145 w100 h25", "测试日志记录")
    LogTestBtn.OnEvent("Click", TestLogger)
    
    ; 分隔线
    CreateSeparator(DemoGui, 10, 175, 480)
    
    ; 状态指示器演示
    DemoGui.Add("Text", "x10 y185 w150 h20", "状态指示器演示:")
    local StatusIndicator1 := DemoGui.Add("Text", "x170 y185 w80 h20", "[正常]")
    local StatusIndicator2 := DemoGui.Add("Text", "x260 y185 w80 h20", "[警告]")
    local StatusIndicator3 := DemoGui.Add("Text", "x350 y185 w80 h20", "[错误]")
    
    ; 设置状态指示器颜色
    UpdateStatusIndicator(StatusIndicator1, true, "[正常]", "[异常]")
    StatusIndicator2.Text := "[警告]"
    StatusIndicator2.Opt("+cOrange")
    UpdateStatusIndicator(StatusIndicator3, false, "[正常]", "[错误]")
    
    ; 按钮组演示
    DemoGui.Add("Text", "x10 y215 w150 h20", "按钮组功能演示:")
    local ButtonTexts := ["按钮1", "按钮2", "按钮3"]
    local ButtonHandlers := Map()
    ButtonHandlers[1] := (*) => ShowInfoMessage("按钮1被点击")
    ButtonHandlers[2] := (*) => ShowWarningMessage("按钮2被点击")
    ButtonHandlers[3] := (*) => ShowErrorMessage("按钮3被点击")
    
    CreateButtonGroup(DemoGui, 10, 240, 480, 30, ButtonTexts, ButtonHandlers)
    
    ; 日志显示区域
    DemoGui.Add("Text", "x10 y280 w100 h20", "日志输出:")
    local LogDisplay := DemoGui.Add("Edit", "x10 y300 w480 h60 ReadOnly VScroll", "演示程序日志将显示在这里...`n")
    
    ; 状态栏
    local StatusBar := DemoGui.Add("StatusBar", "", "演示程序就绪")
    SetStatusBarText(StatusBar, "功能演示界面已加载")
    
    ; 存储控件引用供其他函数使用
    DemoGui.LogDisplay := LogDisplay
    DemoGui.StatusBar := StatusBar
    DemoGui.ProcessResult := ProcessResult
}

; 测试进程检测功能
TestProcessDetection(*) {
    Logger.Info("开始测试进程检测功能")
    
    ; 测试常见的系统进程
    local TestProcesses := ["explorer.exe", "winlogon.exe", "notepad.exe", "chrome.exe"]
    local Results := []
    
    for ProcessName in TestProcesses {
        local Exists := ProcessExists(ProcessName)
        Results.Push(ProcessName . ": " . (Exists ? "存在" : "不存在"))
        Logger.Debug("进程检测: " . ProcessName . " = " . (Exists ? "存在" : "不存在"))
    }
    
    ; 显示结果
    local ResultText := "进程检测结果:`n" . StrJoin(Results, "`n")
    DemoGui.LogDisplay.Text .= "`n" . ResultText . "`n"
    DemoGui.ProcessResult.Text := "[检测完成]"
    DemoGui.ProcessResult.Opt("+cGreen")
    
    Logger.Info("进程检测测试完成")
}

; 测试GUI工具功能
TestGuiUtils(*) {
    Logger.Info("测试GUI工具功能")
    
    ; 显示不同类型的消息框
    ShowInfoMessage("这是一个信息消息框")
    
    if (ShowConfirmDialog("是否继续测试警告消息？")) {
        ShowWarningMessage("这是一个警告消息框")
    }
    
    DemoGui.LogDisplay.Text .= "`nGUI工具测试完成`n"
    Logger.Info("GUI工具测试完成")
}

; 测试配置管理功能
TestConfigManager(*) {
    Logger.Info("测试配置管理功能")
    
    ; 写入测试配置
    Config.SetValue("测试设置", "TestKey1", "TestValue1")
    Config.SetValue("测试设置", "TestKey2", "123")
    Config.SetValue("测试设置", "TestKey3", "true")
    
    ; 保存配置
    Config.SaveConfig()
    
    ; 读取配置
    local Value1 := Config.GetValue("测试设置", "TestKey1", "默认值")
    local Value2 := Config.GetInt("测试设置", "TestKey2", 0)
    local Value3 := Config.GetBool("测试设置", "TestKey3", false)
    
    ; 显示结果
    local ResultText := "配置测试结果:`n"
    ResultText .= "字符串值: " . Value1 . "`n"
    ResultText .= "整数值: " . Value2 . "`n"
    ResultText .= "布尔值: " . (Value3 ? "true" : "false") . "`n"
    
    DemoGui.LogDisplay.Text .= "`n" . ResultText
    
    Logger.Info("配置管理测试完成")
}

; 测试日志系统功能
TestLogger(*) {
    Logger.Info("开始测试日志系统功能")
    
    ; 记录不同级别的日志
    Logger.Debug("这是一条调试信息")
    Logger.Info("这是一条普通信息")
    Logger.Warning("这是一条警告信息")
    Logger.Error("这是一条错误信息")
    
    ; 显示日志文件信息
    local LogSize := Logger.GetFileSize()
    local LogInfo := "日志系统测试完成`n日志文件大小: " . LogSize . " MB`n"
    
    DemoGui.LogDisplay.Text .= "`n" . LogInfo
    SetStatusBarText(DemoGui.StatusBar, "日志测试完成，文件大小: " . LogSize . " MB")
    
    Logger.Info("日志系统测试完成")
}

; 字符串连接辅助函数
StrJoin(Array, Separator) {
    local Result := ""
    Loop Array.Length {
        if (A_Index > 1) {
            Result .= Separator
        }
        Result .= Array[A_Index]
    }
    return Result
}
