#Requires AutoHotkey v2.0
#DllLoad "lua54.dll"

; 主函数 - 测试Lua DLL调用
TestLuaDLL()

; Lua DLL测试函数
TestLuaDLL() {
    ; 创建Lua状态机
    local L := DllCall("lua54\luaL_newstate", "ptr")
    if (!L) {
        throw Error("无法创建 Lua 状态机")
    }

    try {
        ; 加载Lua标准库
        DllCall("lua54\luaL_openlibs", "ptr", L)

        ; 要执行的Lua代码
        local code := 'print("Hello from AHK via Lua54.dll")'

        ; 编译代码
        local result := DllCall("lua54\luaL_loadstring", "ptr", L, "astr", code, "cdecl int")
        if (result != 0) {
            local errorMsg := StrGet(DllCall("lua54\lua_tostring", "ptr", L, "int", -1, "ptr"), "UTF-8")
            throw Error("Lua 编译错误: " . errorMsg)
        }

        ; 执行代码
        result := DllCall("lua54\lua_pcall", "ptr", L, "int", 0, "int", -1, "int", 0, "cdecl int")
        if (result != 0) {
            local errorMsg := StrGet(DllCall("lua54\lua_tostring", "ptr", L, "int", -1, "ptr"), "UTF-8")
            throw Error("Lua 运行错误: " . errorMsg)
        }

        ; 显示成功消息
        MsgBox("Lua代码执行成功！", "信息", "Icon!")

    } catch Error as e {
        ; 显示错误信息
        MsgBox("错误: " . e.Message, "Lua DLL 错误", "IconX")
    } finally {
        ; 确保清理Lua状态机
        if (L) {
            DllCall("lua54\lua_close", "ptr", L)
        }
    }
}