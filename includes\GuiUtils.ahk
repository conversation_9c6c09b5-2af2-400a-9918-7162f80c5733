#Requires AutoHotkey v2.0

; GUI工具类 - 提供GUI界面相关的实用函数
; 包含状态指示器更新、消息框显示等功能

; 更新状态指示器的显示
UpdateStatusIndicator(IndicatorControl, Status, TrueText, FalseText) {
    ; 检查控件是否存在且为对象
    if (!IndicatorControl || !IsObject(IndicatorControl)) {
        return
    }

    try {
        if (Status) {
            IndicatorControl.Text := TrueText
            IndicatorControl.Opt("+cGreen")
        } else {
            IndicatorControl.Text := FalseText
            IndicatorControl.Opt("+cRed")
        }
    } catch Error as e {
        ; 如果更新失败，输出调试信息
        OutputDebug("UpdateStatusIndicator Error: " . e.Message)
    }
}

; 显示信息消息框
ShowInfoMessage(Message, Title := "信息") {
    MsgBox(Message, Title, "Icon!")
}

; 显示错误消息框
ShowErrorMessage(Message, Title := "错误") {
    MsgBox(Message, Title, "IconX")
}

; 显示警告消息框
ShowWarningMessage(Message, Title := "警告") {
    MsgBox(Message, Title, "Icon!")
}

; 显示确认对话框
ShowConfirmDialog(Message, Title := "确认") {
    local Result := MsgBox(Message, Title, "YesNo Icon?")
    return (Result = "Yes")
}

; 创建带有状态栏的GUI窗口
CreateGUIWithStatusBar(Title, Width := 400, Height := 300) {
    local NewGui := Gui("+Resize", Title)
    local StatusBar := NewGui.Add("StatusBar", "", "就绪")
    
    return {Gui: NewGui, StatusBar: StatusBar}
}

; 设置状态栏文本
SetStatusBarText(StatusBar, Text) {
    if (StatusBar) {
        StatusBar.SetText(Text)
    }
}

; 居中显示GUI窗口
CenterGUI(GuiObj) {
    if (!GuiObj) {
        return
    }
    
    ; 获取屏幕尺寸
    local ScreenWidth := A_ScreenWidth
    local ScreenHeight := A_ScreenHeight
    
    ; 获取GUI尺寸
    GuiObj.GetPos(, , &GuiWidth, &GuiHeight)
    
    ; 计算居中位置
    local CenterX := (ScreenWidth - GuiWidth) // 2
    local CenterY := (ScreenHeight - GuiHeight) // 2
    
    ; 移动GUI到居中位置
    GuiObj.Move(CenterX, CenterY)
}

; 创建分隔线控件
CreateSeparator(GuiObj, X, Y, Width) {
    return GuiObj.Add("Text", "x" . X . " y" . Y . " w" . Width . " h2 0x10")
}

; 创建标题文本控件
CreateTitleText(GuiObj, X, Y, Width, Text) {
    local TitleControl := GuiObj.Add("Text", "x" . X . " y" . Y . " w" . Width . " h30 Center Section", Text)
    TitleControl.SetFont("s12 Bold")
    return TitleControl
}

; 创建状态指示器组
CreateStatusGroup(GuiObj, X, Y, LabelText, StatusText) {
    local Label := GuiObj.Add("Text", "x" . X . " y" . Y . " w100 h20", LabelText)
    local Status := GuiObj.Add("Text", "x" . (X + 110) . " y" . Y . " w150 h20", StatusText)
    
    return {Label: Label, Status: Status}
}

; 创建按钮组
CreateButtonGroup(GuiObj, X, Y, Width, Height, ButtonTexts, EventHandlers) {
    local Buttons := []
    local ButtonWidth := Width // ButtonTexts.Length
    
    Loop ButtonTexts.Length {
        local BtnX := X + (A_Index - 1) * ButtonWidth
        local Btn := GuiObj.Add("Button", "x" . BtnX . " y" . Y . " w" . (ButtonWidth - 5) . " h" . Height, ButtonTexts[A_Index])
        
        if (EventHandlers.Has(A_Index)) {
            Btn.OnEvent("Click", EventHandlers[A_Index])
        }
        
        Buttons.Push(Btn)
    }
    
    return Buttons
}

; 安全更新控件文本
SafeUpdateControlText(Control, Text) {
    if (Control && IsObject(Control)) {
        try {
            Control.Text := Text
        } catch Error as e {
            OutputDebug("SafeUpdateControlText Error: " . e.Message)
        }
    }
}

; 启用/禁用控件
SetControlEnabled(Control, Enabled) {
    if (Control && IsObject(Control)) {
        try {
            Control.Enabled := Enabled
        } catch Error as e {
            OutputDebug("SetControlEnabled Error: " . e.Message)
        }
    }
}

; 批量启用/禁用控件
SetControlsEnabled(Controls, Enabled) {
    for Control in Controls {
        SetControlEnabled(Control, Enabled)
    }
}
